import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../payment/payment_provider.dart';

/// Purchase button widget for buying the full version
/// Shows different states based on purchase status and loading state
class PurchaseButton extends StatelessWidget {
  // For now, using a placeholder mobile number
  // TODO: Get actual user mobile number from authentication system
  static const String _placeholderMobile = '09123456789';

  const PurchaseButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<PaymentProvider>(
      builder: (context, paymentProvider, child) {
        // If already purchased, show disabled button
        if (paymentProvider.isPurchased) {
          return _buildPurchasedButton(context);
        }

        // If loading, show progress indicator
        if (paymentProvider.isLoading) {
          return _buildLoadingButton(context);
        }

        // Show active purchase button
        return _buildPurchaseButton(context, paymentProvider);
      },
    );
  }

  /// Build button for already purchased state
  Widget _buildPurchasedButton(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: ElevatedButton.icon(
        onPressed: null, // Disabled
        icon: const Icon(
          Icons.check_circle,
          color: Colors.green,
        ),
        label: const Text(
          'خریداری شده',
          style: TextStyle(
            fontFamily: 'Samim',
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          textDirection: TextDirection.rtl,
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.grey[300],
          foregroundColor: Colors.grey[600],
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
      ),
    );
  }

  /// Build loading button
  Widget _buildLoadingButton(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: ElevatedButton(
        onPressed: null, // Disabled during loading
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.tealAccent[400],
          foregroundColor: Colors.black,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'در حال پردازش...',
              style: TextStyle(
                fontFamily: 'Samim',
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textDirection: TextDirection.rtl,
            ),
          ],
        ),
      ),
    );
  }

  /// Build active purchase button
  Widget _buildPurchaseButton(BuildContext context, PaymentProvider paymentProvider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: ElevatedButton.icon(
        onPressed: () => _handlePurchase(context, paymentProvider),
        icon: const Icon(
          Icons.shopping_cart,
          color: Colors.black,
        ),
        label: const Text(
          'خرید نسخه کامل',
          style: TextStyle(
            fontFamily: 'Samim',
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
          textDirection: TextDirection.rtl,
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.tealAccent[400],
          foregroundColor: Colors.black,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
      ),
    );
  }

  /// Handle purchase button press
  void _handlePurchase(BuildContext context, PaymentProvider paymentProvider) {
    // Show confirmation dialog before purchase
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'تایید خرید',
            style: TextStyle(fontFamily: 'Samim'),
            textDirection: TextDirection.rtl,
          ),
          content: const Text(
            'آیا مایل به خرید نسخه کامل هستید؟',
            style: TextStyle(fontFamily: 'Samim'),
            textDirection: TextDirection.rtl,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'انصراف',
                style: TextStyle(fontFamily: 'Samim'),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Start purchase process
                paymentProvider.purchaseProduct(_placeholderMobile);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.tealAccent[400],
                foregroundColor: Colors.black,
              ),
              child: const Text(
                'خرید',
                style: TextStyle(fontFamily: 'Samim'),
              ),
            ),
          ],
        );
      },
    );
  }
}
