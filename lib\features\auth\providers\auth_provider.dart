import 'package:flutter/material.dart';
import '../../../core/services/api_service.dart';
import '../../../core/services/storage_service.dart';

/// Provider for managing authentication state and API calls
/// Handles OTP sending and verification with loading states
class AuthProvider extends ChangeNotifier {
  final ApiService _apiService;
  final StorageService _storageService;

  // Private state variables
  bool _isLoading = false;

  // Constructor - requires ApiService and StorageService instances
  AuthProvider(this._apiService, this._storageService);

  // Public getters
  bool get isLoading => _isLoading;

  /// Sends OTP to the specified mobile number
  /// Returns true if successful, false otherwise
  /// Manages loading state automatically
  Future<bool> sendOtp(String mobile) async {
    try {
      // Set loading state
      _isLoading = true;
      notifyListeners();

      // Call API service
      final result = await _apiService.sendOtp(mobile);
      return result;
    } finally {
      // Always reset loading state
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Verifies the OTP for the specified mobile number
  /// Returns true if verification successful, false otherwise
  /// Manages loading state automatically and saves login status
  Future<bool> verifyOtp(String mobile, String otp) async {
    try {
      // Set loading state
      _isLoading = true;
      notifyListeners();

      // Call API service
      final result = await _apiService.verifyOtp(mobile, otp);

      // If verification successful, save login status
      if (result) {
        try {
          await _storageService.saveUserLoggedIn(true);
        } catch (e) {
          // Log storage error but don't fail the login
          print('Warning: Failed to save login status to storage: $e');
        }
      }

      return result;
    } finally {
      // Always reset loading state
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Check if user is logged in from storage
  bool get isLoggedIn => _storageService.isUserLoggedIn();

  /// Logout user and clear storage
  Future<void> logout() async {
    try {
      await _storageService.saveUserLoggedIn(false);
      notifyListeners();
    } catch (e) {
      print('Warning: Failed to clear login status from storage: $e');
    }
  }
}
