import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// Central storage service using singleton pattern
/// Handles authentication state with SharedPreferences
/// Handles lesson progress and quiz results with Hive
class StorageService {
  // Singleton instance
  static final StorageService _instance = StorageService._internal();

  // Private constructor
  StorageService._internal();

  // Public getter for singleton access
  static StorageService get instance => _instance;

  // Storage instances
  SharedPreferences? _prefs;
  Box? _userProgressBox;
  Box? _settingsBox;

  // Storage keys
  static const String _keyUserLoggedIn = 'user_logged_in';
  static const String _keyCompletedLessons = 'completed_lessons';
  static const String _keyQuizResults = 'quiz_results';
  static const String _keyPurchaseState = 'purchase_state';

  /// Initialize storage services
  /// Must be called before using any storage methods
  Future<void> init() async {
    try {
      // Initialize SharedPreferences
      _prefs = await SharedPreferences.getInstance();

      // Open Hive boxes
      _userProgressBox = await Hive.openBox('user_progress');
      _settingsBox = await Hive.openBox('settings');

      print('StorageService initialized successfully');
    } catch (e) {
      print('Error initializing StorageService: $e');
      rethrow;
    }
  }

  /// Check if storage is initialized
  bool get isInitialized =>
      _prefs != null && _userProgressBox != null && _settingsBox != null;

  // ==================== Authentication Methods ====================

  /// Save user login status
  Future<void> saveUserLoggedIn(bool isLoggedIn) async {
    if (!isInitialized) {
      throw Exception('StorageService not initialized. Call init() first.');
    }

    try {
      await _prefs!.setBool(_keyUserLoggedIn, isLoggedIn);
      print('User login status saved: $isLoggedIn');
    } catch (e) {
      print('Error saving user login status: $e');
      rethrow;
    }
  }

  /// Get user login status
  bool isUserLoggedIn() {
    if (!isInitialized) {
      print(
          'StorageService not initialized. Returning false for login status.');
      return false;
    }

    try {
      return _prefs!.getBool(_keyUserLoggedIn) ?? false;
    } catch (e) {
      print('Error getting user login status: $e');
      return false;
    }
  }

  // ==================== Purchase State Methods ====================

  /// Save purchase state
  Future<void> savePurchaseState(bool isPurchased) async {
    if (!isInitialized) {
      throw Exception('StorageService not initialized. Call init() first.');
    }

    try {
      await _prefs!.setBool(_keyPurchaseState, isPurchased);
      print('Purchase state saved: $isPurchased');
    } catch (e) {
      print('Error saving purchase state: $e');
      rethrow;
    }
  }

  /// Get purchase state
  bool getPurchaseState() {
    if (!isInitialized) {
      print(
          'StorageService not initialized. Returning false for purchase state.');
      return false;
    }

    try {
      return _prefs!.getBool(_keyPurchaseState) ?? false;
    } catch (e) {
      print('Error getting purchase state: $e');
      return false;
    }
  }

  // ==================== Lesson Progress Methods ====================

  /// Mark a lesson as completed
  Future<void> completeLesson(int lessonId) async {
    if (!isInitialized) {
      throw Exception('StorageService not initialized. Call init() first.');
    }

    try {
      // Get current completed lessons
      final completedLessons = getCompletedLessons();

      // Add new lesson ID
      completedLessons.add(lessonId);

      // Save back to storage
      await _userProgressBox!
          .put(_keyCompletedLessons, completedLessons.toList());

      print('Lesson $lessonId marked as completed');
    } catch (e) {
      print('Error completing lesson $lessonId: $e');
      rethrow;
    }
  }

  /// Get set of completed lesson IDs
  Set<int> getCompletedLessons() {
    if (!isInitialized) {
      print(
          'StorageService not initialized. Returning empty set for completed lessons.');
      return <int>{};
    }

    try {
      final List<dynamic>? completedList =
          _userProgressBox!.get(_keyCompletedLessons);

      if (completedList == null) {
        return <int>{};
      }

      // Convert to Set<int> safely
      return completedList.whereType<int>().toSet();
    } catch (e) {
      print('Error getting completed lessons: $e');
      return <int>{};
    }
  }

  // ==================== Quiz Results Methods ====================

  /// Save quiz result for a specific lesson
  Future<void> saveQuizResult(int lessonId, int score) async {
    if (!isInitialized) {
      throw Exception('StorageService not initialized. Call init() first.');
    }

    try {
      // Get current quiz results
      final Map<String, dynamic> quizResults = _getQuizResultsMap();

      // Save new result
      quizResults[lessonId.toString()] = score;

      // Save back to storage
      await _userProgressBox!.put(_keyQuizResults, quizResults);

      print('Quiz result saved for lesson $lessonId: $score');
    } catch (e) {
      print('Error saving quiz result for lesson $lessonId: $e');
      rethrow;
    }
  }

  /// Get quiz result for a specific lesson
  int? getQuizResult(int lessonId) {
    if (!isInitialized) {
      print('StorageService not initialized. Returning null for quiz result.');
      return null;
    }

    try {
      final Map<String, dynamic> quizResults = _getQuizResultsMap();
      final dynamic result = quizResults[lessonId.toString()];

      return result is int ? result : null;
    } catch (e) {
      print('Error getting quiz result for lesson $lessonId: $e');
      return null;
    }
  }

  /// Get all quiz results as a map
  Map<int, int> getAllQuizResults() {
    if (!isInitialized) {
      print(
          'StorageService not initialized. Returning empty map for quiz results.');
      return <int, int>{};
    }

    try {
      final Map<String, dynamic> quizResults = _getQuizResultsMap();
      final Map<int, int> results = <int, int>{};

      quizResults.forEach((key, value) {
        final int? lessonId = int.tryParse(key);
        if (lessonId != null && value is int) {
          results[lessonId] = value;
        }
      });

      return results;
    } catch (e) {
      print('Error getting all quiz results: $e');
      return <int, int>{};
    }
  }

  // ==================== Private Helper Methods ====================

  /// Get quiz results map from storage
  Map<String, dynamic> _getQuizResultsMap() {
    try {
      final dynamic storedResults = _userProgressBox!.get(_keyQuizResults);

      if (storedResults is Map) {
        return Map<String, dynamic>.from(storedResults);
      }

      return <String, dynamic>{};
    } catch (e) {
      print('Error getting quiz results map: $e');
      return <String, dynamic>{};
    }
  }

  // ==================== Utility Methods ====================

  /// Clear all stored data (useful for logout or reset)
  Future<void> clearAllData() async {
    if (!isInitialized) {
      throw Exception('StorageService not initialized. Call init() first.');
    }

    try {
      // Clear SharedPreferences
      await _prefs!.clear();

      // Clear Hive boxes
      await _userProgressBox!.clear();
      await _settingsBox!.clear();

      print('All storage data cleared');
    } catch (e) {
      print('Error clearing storage data: $e');
      rethrow;
    }
  }

  /// Get storage statistics for debugging
  Map<String, dynamic> getStorageStats() {
    if (!isInitialized) {
      return {'error': 'StorageService not initialized'};
    }

    try {
      return {
        'isLoggedIn': isUserLoggedIn(),
        'completedLessonsCount': getCompletedLessons().length,
        'quizResultsCount': getAllQuizResults().length,
        'userProgressBoxLength': _userProgressBox!.length,
        'settingsBoxLength': _settingsBox!.length,
      };
    } catch (e) {
      return {'error': 'Error getting storage stats: $e'};
    }
  }
}
